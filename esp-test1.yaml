esphome:
  name: esp-test1
  friendly_name: "ESP8266 Điề<PERSON>ô<PERSON>"
  comment: "<PERSON>ệ thống điều khiển cửa RFID và rèm tự động"
  on_boot:
    priority: 600
    then:
      - servo.write:
          id: door_servo
          level: 3%
      - logger.log: "Khởi động hoàn tất, cửa đã được đóng"

esp8266:
  board: nodemcu

logger:
  level: DEBUG
  baud_rate: 0

wifi:
  networks:
    - ssid: !secret wifi_ssid
      password: !secret wifi_password
      priority: 100.0
    - ssid: "Hayla"
      password: "????????"
      priority: 80.0
    - ssid: "host-local"
      password: "12345678"
      priority: 60.0
  ap:
    ssid: "ESP-Test1 Fallback Hotspot"
    password: "Ba3XYIHUG7PX"
    ap_timeout: 5min

api:
  encryption:
    key: "pPKySKyykkL2cL76njHXlwhCu4vp1J5Oxr56mPNeDBo="
  reboot_timeout: 15min

ota:
  - platform: esphome
    password: "04fffa42f4ab339d65b5ff3b7da8d0b2"

captive_portal:

# ================= SPI & RFID =================
spi:
  clk_pin: GPIO14  # SCK → D5
  miso_pin: GPIO12 # MISO → D6
  mosi_pin: GPIO13 # MOSI → D7

rc522_spi:
  cs_pin: GPIO15   # SDA → D8
  reset_pin: GPIO4 # RST → D2
  update_interval: 250ms
  on_tag:
    then:
      - lambda: |-
          if (x == "74-10-37-94") {
            ESP_LOGI("rc522", "Thẻ RFID hợp lệ, đang mở cửa...");
            id(door_servo_switch).turn_on();
            return;
          }
          ESP_LOGW("rc522", "Thẻ RFID không hợp lệ!");

# ================= SERVO & LED =================
output:
  - platform: gpio
    pin: GPIO5  # đổi từ GPIO15 sang GPIO5 (D1)
    id: led1_output
  - platform: esp8266_pwm
    pin: GPIO16
    frequency: 50 Hz
    id: servo_output

servo:
  - id: door_servo
    output: servo_output

light:
  - platform: binary
    name: "Đèn LED Phòng Khách"
    output: led1_output

# ================= SWITCHES =================
switch:
  - platform: template
    name: "Công Tắc Servo Cửa"
    id: door_servo_switch
    turn_on_action:
      - servo.write:
          id: door_servo
          level: 50%
      - delay: 5s
      - servo.write:
          id: door_servo
          level: 3%

  - platform: gpio
    pin: GPIO0
    name: "Động Cơ Rèm IN1"
    id: curtain_motor_in1
    interlock: &interlock_group [curtain_motor_in1, curtain_motor_in2]

  - platform: gpio
    pin: GPIO2
    name: "Động Cơ Rèm IN2"
    id: curtain_motor_in2
    interlock: *interlock_group

  - platform: template
    name: "Rèm Phòng Khách"
    id: living_room_curtain
    turn_on_action:
      - switch.turn_on: curtain_motor_in1
      - delay: 10s
      - switch.turn_off: curtain_motor_in1
    turn_off_action:
      - switch.turn_on: curtain_motor_in2
      - delay: 10s
      - switch.turn_off: curtain_motor_in2

# ================= CẢM BIẾN =================
sensor:
  - platform: uptime
    name: "Thời Gian Hoạt Động"
  - platform: wifi_signal
    name: "Cường Độ WiFi"
    update_interval: 60s

# ================= NÚT KHỞI ĐỘNG LẠI =================
button:
  - platform: restart
    name: "Khởi Động Lại Thiết Bị"
    id: restart_button

# ================= TỰ ĐỘNG KHỞI ĐỘNG LẠI HẰNG NGÀY =================
interval:
  - interval: 24h
    then:
      - button.press: restart_button
